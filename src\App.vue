<script setup lang="ts">
import { ref, computed } from 'vue'
import { marked } from 'marked'
import TurndownService from 'turndown'
import * as showdown from 'showdown'

// 输入的 Markdown 文本
const markdownInput = ref(`# Showdown.js 使用示例

## 基本语法测试

**粗体文本** 和 __另一种粗体__

*斜体文本* 和 _另一种斜体_

~~删除线文本~~

### 列表测试

无序列表:
- 项目1
- 项目2
  - 子项目1
  - 子项目2

有序列表:
1. 第一项
2. 第二项
   1. 子项目1
   2. 子项目2

### 任务列表
- [x] 已完成任务
- [ ] 未完成任务

### 表格
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| **粗体** | *斜体* | ~~删除~~ |

### 代码块
\`\`\`javascript
function hello() {
  console.log('Hello, Showdown!');
}
\`\`\`

### 链接和图片
[GitHub](https://github.com)

> 这是一个引用块
> 可以包含多行内容`)

// HTML 输出
const htmlOutput = ref('')

// 转换回的 Markdown
const convertedMarkdown = ref('')

// 创建 showdown 转换器
const showdownConverter = new showdown.Converter({
  tables: true,
  strikethrough: true,
  tasklists: true,
  ghCodeBlocks: true,
  emoji: true,
  headerLevelStart: 1,
  simplifiedAutoLink: true,
  literalMidWordUnderscores: true,
  literalMidWordAsterisks: true,
  ghCompatibleHeaderId: true,
  parseImgDimensions: true,
  openLinksInNewWindow: false,
  backslashEscapesHTMLTags: true
})

// 创建 marked 转换器（用于对比）
const markedOptions = {
  gfm: true,
  breaks: false,
  pedantic: false,
  sanitize: false,
  smartLists: true,
  smartypants: false
}

// 创建 turndown 转换器
const turndownService = new TurndownService({
  headingStyle: 'atx',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**',
  linkStyle: 'inlined',
  linkReferenceStyle: 'full'
})

// 使用 showdown 转换
const showdownHtml = computed(() => {
  try {
    return showdownConverter.makeHtml(markdownInput.value)
  } catch (error) {
    return `转换错误: ${error}`
  }
})

// 使用 marked 转换（对比）
const markedHtml = ref('')

// 异步转换 marked
const convertWithMarked = async () => {
  try {
    const result = await marked(markdownInput.value, markedOptions)
    markedHtml.value = typeof result === 'string' ? result : String(result)
  } catch (error) {
    markedHtml.value = `转换错误: ${error}`
  }
}

// 转换函数
const convertToHtml = async (useShowdown: boolean = true) => {
  if (useShowdown) {
    htmlOutput.value = showdownHtml.value
  } else {
    await convertWithMarked()
    htmlOutput.value = markedHtml.value
  }
}

const convertBackToMarkdown = () => {
  if (htmlOutput.value) {
    try {
      convertedMarkdown.value = turndownService.turndown(htmlOutput.value)
    } catch (error) {
      convertedMarkdown.value = `转换错误: ${error}`
    }
  }
}

// 比较原始和转换后的差异
const compareMarkdown = computed(() => {
  const original = markdownInput.value.trim()
  const converted = convertedMarkdown.value.trim()

  if (original === converted) {
    return '✅ 完全一致'
  } else {
    return '❌ 存在差异'
  }
})

// 初始转换
convertToHtml(true)
convertWithMarked() // 预加载 marked 转换结果
</script>

<template>
  <div class="container">
    <h1>Markdown ↔ HTML 双向转换测试</h1>

    <div class="converter-section">
      <h2>1. 原始 Markdown 输入</h2>
      <textarea
        v-model="markdownInput"
        class="input-area"
        placeholder="输入 Markdown 文本..."
        @input="() => convertToHtml(true)"
      ></textarea>
    </div>

    <div class="buttons">
      <button @click="convertToHtml(true)" class="btn primary">
        使用 Showdown 转换为 HTML
      </button>
      <button @click="convertToHtml(false)" class="btn secondary">
        使用 Marked 转换为 HTML (对比)
      </button>
      <button @click="convertBackToMarkdown" class="btn success">
        转换回 Markdown
      </button>
    </div>

    <div class="output-section">
      <div class="output-column">
        <h2>2. HTML 输出</h2>
        <div class="html-preview" v-html="htmlOutput"></div>
        <details class="html-source">
          <summary>查看 HTML 源码</summary>
          <pre><code>{{ htmlOutput }}</code></pre>
        </details>
      </div>

      <div class="output-column">
        <h2>3. 转换回的 Markdown</h2>
        <div class="comparison-status">{{ compareMarkdown }}</div>
        <pre class="markdown-output">{{ convertedMarkdown }}</pre>
      </div>
    </div>

    <div class="comparison-section">
      <h2>4. Showdown vs Marked 对比</h2>
      <div class="comparison-grid">
        <div>
          <h3>Showdown 输出</h3>
          <div class="html-preview" v-html="showdownHtml"></div>
        </div>
        <div>
          <h3>Marked 输出</h3>
          <div class="html-preview" v-html="markedHtml"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #34495e;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.converter-section {
  margin-bottom: 20px;
}

.input-area {
  width: 100%;
  height: 300px;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
  box-sizing: border-box;
}

.input-area:focus {
  border-color: #3498db;
  outline: none;
}

.buttons {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn.primary {
  background-color: #3498db;
  color: white;
}

.btn.primary:hover {
  background-color: #2980b9;
}

.btn.secondary {
  background-color: #95a5a6;
  color: white;
}

.btn.secondary:hover {
  background-color: #7f8c8d;
}

.btn.success {
  background-color: #27ae60;
  color: white;
}

.btn.success:hover {
  background-color: #229954;
}

.output-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.output-column {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.html-preview {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
  min-height: 200px;
  overflow-y: auto;
  max-height: 400px;
}

.html-source {
  margin-top: 10px;
}

.html-source pre {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.markdown-output {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.comparison-status {
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-weight: bold;
  text-align: center;
}

.comparison-section {
  margin-top: 30px;
  border-top: 2px solid #eee;
  padding-top: 20px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.comparison-grid > div {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .output-section,
  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* HTML 预览样式 */
.html-preview h1,
.html-preview h2,
.html-preview h3 {
  margin-top: 0;
}

.html-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

.html-preview th,
.html-preview td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.html-preview th {
  background-color: #f2f2f2;
}

.html-preview blockquote {
  border-left: 4px solid #3498db;
  margin: 0;
  padding-left: 15px;
  color: #666;
}

.html-preview code {
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.html-preview pre {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.html-preview ul[data-task-list] {
  list-style: none;
  padding-left: 0;
}

.html-preview ul[data-task-list] li {
  margin: 5px 0;
}
</style>

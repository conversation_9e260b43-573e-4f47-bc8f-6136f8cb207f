# Showdown.js 使用指南

## 概述

Showdown.js 是一个 JavaScript Markdown 到 HTML 的转换器，基于 John Gruber 的原始作品。虽然文档中声称是"双向转换器"，但实际上它**只支持 Markdown → HTML 的单向转换**。

## 安装

```bash
npm install showdown
npm install @types/showdown  # TypeScript 类型定义
```

## 基本使用

### 1. 导入和创建转换器

```javascript
import * as showdown from 'showdown'

// 创建转换器实例
const converter = new showdown.Converter()

// 转换 Markdown 为 HTML
const markdown = '# Hello World\n\nThis is **bold** text.'
const html = converter.makeHtml(markdown)
console.log(html)
// 输出: <h1 id="helloworld">Hello World</h1><p>This is <strong>bold</strong> text.</p>
```

### 2. 配置选项

```javascript
const converter = new showdown.Converter({
  // 启用表格支持
  tables: true,
  
  // 启用删除线
  strikethrough: true,
  
  // 启用任务列表
  tasklists: true,
  
  // 启用 GitHub 风格代码块
  ghCodeBlocks: true,
  
  // 启用 emoji 支持
  emoji: true,
  
  // 设置标题起始级别
  headerLevelStart: 1,
  
  // 简化自动链接
  simplifiedAutoLink: true,
  
  // 字面意思的中间单词下划线
  literalMidWordUnderscores: true,
  
  // 字面意思的中间单词星号
  literalMidWordAsterisks: true,
  
  // GitHub 兼容的标题 ID
  ghCompatibleHeaderId: true,
  
  // 解析图片尺寸
  parseImgDimensions: true,
  
  // 在新窗口打开链接
  openLinksInNewWindow: false,
  
  // 反斜杠转义 HTML 标签
  backslashEscapesHTMLTags: true
})
```

### 3. 全局设置选项

```javascript
// 全局设置（影响所有实例）
showdown.setOption('tables', true)
showdown.setOption('strikethrough', true)

// 获取选项
const tablesEnabled = showdown.getOption('tables')

// 获取所有默认选项
const defaultOptions = showdown.getDefaultOptions()
```

### 4. 使用预设风格

```javascript
// 设置为 GitHub 风格
converter.setFlavor('github')

// 可用的风格：
// - 'original': 原始 markdown 风格
// - 'vanilla': showdown 基础风格
// - 'github': GitHub 风格 markdown (GFM)
```

## 双向转换实现

由于 Showdown 只支持单向转换，要实现双向转换需要结合其他库：

### 方案1: Showdown + Turndown

```javascript
import * as showdown from 'showdown'
import TurndownService from 'turndown'

// Markdown → HTML
const showdownConverter = new showdown.Converter({
  tables: true,
  strikethrough: true,
  tasklists: true
})

// HTML → Markdown
const turndownService = new TurndownService({
  headingStyle: 'atx',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**'
})

// 使用示例
const markdown = '**Hello** _world_'
const html = showdownConverter.makeHtml(markdown)
const backToMarkdown = turndownService.turndown(html)

console.log('原始:', markdown)
console.log('HTML:', html)
console.log('转换回:', backToMarkdown)
```

### 方案2: Marked + Turndown

```javascript
import { marked } from 'marked'
import TurndownService from 'turndown'

// 配置 marked
marked.setOptions({
  gfm: true,
  breaks: false,
  pedantic: false,
  sanitize: false,
  smartLists: true,
  smartypants: false
})

// 使用示例
const markdown = '**Hello** _world_'
const html = await marked(markdown)
const backToMarkdown = turndownService.turndown(html)
```

## 常见问题和解决方案

### 1. 格式一致性问题

**问题**: `**粗体**` 和 `__粗体__` 转换后可能不一致

**解决方案**: 配置 turndown 使用统一的分隔符

```javascript
const turndownService = new TurndownService({
  strongDelimiter: '**',  // 统一使用 **
  emDelimiter: '*'        // 统一使用 *
})
```

### 2. 列表格式问题

**解决方案**: 统一列表标记

```javascript
const turndownService = new TurndownService({
  bulletListMarker: '-',  // 统一使用 -
  headingStyle: 'atx'     // 统一使用 # 风格标题
})
```

### 3. 代码块问题

**解决方案**: 使用围栏式代码块

```javascript
const turndownService = new TurndownService({
  codeBlockStyle: 'fenced',
  fence: '```'
})
```

## 最佳实践

1. **选择合适的库组合**:
   - 简单项目: Showdown + Turndown
   - 复杂项目: Marked + Turndown
   - 高度定制: unified 生态系统

2. **配置一致性**:
   - 统一分隔符设置
   - 统一列表标记
   - 统一标题风格

3. **预处理和后处理**:
   - 转换前标准化输入格式
   - 转换后验证输出格式

4. **测试覆盖**:
   - 测试各种 Markdown 语法
   - 测试双向转换一致性
   - 测试边界情况

## 性能考虑

- Showdown 比 Marked 稍慢，但更稳定
- 大文档建议分块处理
- 缓存转换结果以提高性能

## 总结

Showdown.js 是一个可靠的 Markdown 转 HTML 库，但需要配合其他库实现双向转换。通过合理的配置和预处理，可以最大化保持转换的一致性。
